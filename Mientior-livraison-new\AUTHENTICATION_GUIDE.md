# 🔐 Guide Complet d'Authentification - Livraison Afrique

## 📋 **Vue d'Ensemble**

Ce guide détaille l'implémentation complète du système d'authentification pour l'application Livraison Afrique et son intégration avec les fonctionnalités avancées (notifications, chat, tracking, analytics).

## 🏗️ **Architecture d'Authentification**

### **Composants Principaux**
- ✅ **Supabase Auth** - Service d'authentification backend
- ✅ **useAuth Hook** - Gestion d'état côté client
- ✅ **AuthStore** - Store Zustand pour la persistance
- ✅ **RLS Policies** - Sécurité au niveau des données
- ✅ **Role-based Access** - Contrôle d'accès par rôle

### **Flux d'Authentification**
```
1. Utilisateur → Inscription/Connexion
2. Supabase Auth → Validation + JWT
3. useAuth Hook → Mise à jour de l'état
4. AuthStore → Persistance locale
5. RLS Policies → Sécurisation des données
6. Fonctionnalités Avancées → Accès autorisé
```

## 🔧 **Configuration Supabase Auth**

### **1. Configuration Dashboard Supabase**
```sql
-- Dans Supabase SQL Editor
-- 1. Créer la table users étendue
CREATE TABLE IF NOT EXISTS users (
  id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  email TEXT UNIQUE NOT NULL,
  full_name TEXT,
  phone TEXT,
  role TEXT NOT NULL CHECK (role IN ('client', 'livreur', 'marchand', 'admin')),
  avatar_url TEXT,
  is_active BOOLEAN DEFAULT TRUE,
  notification_preferences JSONB DEFAULT '{"orders": true, "delivery": true, "promotions": true, "system": true}',
  last_seen TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. Activer RLS
ALTER TABLE users ENABLE ROW LEVEL SECURITY;

-- 3. Politiques de base
CREATE POLICY "Users can view own profile" ON users
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON users
  FOR UPDATE USING (auth.uid() = id);

-- 4. Fonction pour créer automatiquement le profil utilisateur
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO users (id, email, full_name, role)
  VALUES (
    NEW.id,
    NEW.email,
    COALESCE(NEW.raw_user_meta_data->>'full_name', ''),
    COALESCE(NEW.raw_user_meta_data->>'role', 'client')
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 5. Trigger pour la création automatique
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION handle_new_user();
```

### **2. Configuration Email Templates**
Dans Supabase Dashboard > Authentication > Email Templates :

**Confirmation Email:**
```html
<h2>Bienvenue sur Livraison Afrique !</h2>
<p>Cliquez sur le lien ci-dessous pour confirmer votre compte :</p>
<p><a href="{{ .ConfirmationURL }}">Confirmer mon compte</a></p>
```

**Password Recovery:**
```html
<h2>Réinitialisation de mot de passe</h2>
<p>Cliquez sur le lien ci-dessous pour réinitialiser votre mot de passe :</p>
<p><a href="{{ .ConfirmationURL }}">Réinitialiser mon mot de passe</a></p>
```

## 🎯 **Utilisation du Hook useAuth**

### **Hook useAuth Complet**
```typescript
import { useAuth } from '../hooks/useAuth';

const MyComponent = () => {
  const {
    // État d'authentification
    user,              // Utilisateur connecté (null si non connecté)
    loading,           // État de chargement
    error,             // Erreur d'authentification
    isAuthenticated,   // Booléen de connexion
    
    // Vérification des rôles
    isClient,          // user.role === 'client'
    isLivreur,         // user.role === 'livreur'
    isMarchand,        // user.role === 'marchand'
    isAdmin,           // user.role === 'admin'
    
    // Actions d'authentification
    signIn,            // (email, password) => Promise<void>
    signUp,            // (email, password, userData) => Promise<void>
    signOut,           // () => Promise<void>
    
    // Gestion du profil
    updateProfile,     // (updates) => Promise<void>
    uploadAvatar,      // (file) => Promise<string>
    
    // Récupération de mot de passe
    resetPassword,     // (email) => Promise<void>
    updatePassword,    // (newPassword) => Promise<void>
    
    // Utilitaires
    initialize,        // () => Promise<void>
    refreshSession,    // () => Promise<void>
  } = useAuth();

  // Exemple d'utilisation
  useEffect(() => {
    if (isAuthenticated && user) {
      console.log(`Utilisateur connecté: ${user.full_name} (${user.role})`);
    }
  }, [isAuthenticated, user]);

  return (
    <View>
      {isAuthenticated ? (
        <AuthenticatedContent user={user} />
      ) : (
        <LoginScreen />
      )}
    </View>
  );
};
```

### **Exemples d'Utilisation par Rôle**
```typescript
// Composant avec contrôle d'accès
const DashboardComponent = () => {
  const { user, isClient, isLivreur, isMarchand, isAdmin } = useAuth();

  if (isClient) {
    return <ClientDashboard user={user} />;
  } else if (isLivreur) {
    return <LivreurDashboard user={user} />;
  } else if (isMarchand) {
    return <MarchandDashboard user={user} />;
  } else if (isAdmin) {
    return <AdminDashboard user={user} />;
  } else {
    return <AccessDenied />;
  }
};

// Hook personnalisé pour les permissions
const usePermissions = () => {
  const { user, isAuthenticated } = useAuth();

  return {
    canCreateOrder: isAuthenticated && user?.role === 'client',
    canAcceptDelivery: isAuthenticated && user?.role === 'livreur',
    canManageProducts: isAuthenticated && user?.role === 'marchand',
    canViewAllAnalytics: isAuthenticated && ['admin', 'marchand'].includes(user?.role),
    canModerateChat: isAuthenticated && ['admin', 'support'].includes(user?.role),
  };
};
```

## 🔐 **Intégration avec les Fonctionnalités Avancées**

### **1. Notifications Push + Auth**
```typescript
// Initialisation des notifications liées à l'utilisateur
const NotificationManager = () => {
  const { user, isAuthenticated } = useAuth();

  useEffect(() => {
    if (isAuthenticated && user) {
      // Enregistrer le token push pour cet utilisateur
      NotificationService.initialize(user.id);
      
      // Configurer les préférences de notification
      NotificationService.updatePreferences(user.notification_preferences);
    }
  }, [isAuthenticated, user]);

  return null;
};

// Envoi de notifications basées sur le rôle
const sendRoleBasedNotification = async (orderId: string) => {
  const { data: order } = await supabase
    .from('commandes')
    .select('client_id, livreur_id, merchant_id')
    .eq('id', orderId)
    .single();

  // Notifier le client
  await NotificationService.notifyOrderStatusChange(
    order.client_id,
    orderId,
    'confirmee',
    'Restaurant Délice'
  );

  // Notifier le livreur si assigné
  if (order.livreur_id) {
    await NotificationService.notifyDeliveryUpdate(
      order.livreur_id,
      orderId,
      'Nouvelle livraison assignée'
    );
  }
};
```

### **2. Chat + Permissions**
```typescript
// Chat avec contrôle d'accès
const ChatComponent = ({ deliveryId }: { deliveryId: string }) => {
  const { user, isAuthenticated } = useAuth();
  const { messages, participants, sendMessage } = useChat(deliveryId);

  // Vérifier les permissions d'accès
  const canAccessChat = useMemo(() => {
    if (!isAuthenticated || !user) return false;
    
    // Vérifier si l'utilisateur est participant à cette livraison
    return participants.some(p => p.user_id === user.id);
  }, [isAuthenticated, user, participants]);

  const handleSendMessage = async (content: string) => {
    if (!canAccessChat) return;
    
    await sendMessage(content, 'text');
    
    // Notifier les autres participants
    const otherParticipants = participants.filter(p => p.user_id !== user.id);
    for (const participant of otherParticipants) {
      await NotificationService.notifyNewMessage(
        participant.user_id,
        user.full_name,
        content,
        deliveryId
      );
    }
  };

  if (!canAccessChat) {
    return (
      <View style={styles.accessDenied}>
        <Text>Vous n'avez pas accès à ce chat</Text>
      </View>
    );
  }

  return <ChatInterface onSendMessage={handleSendMessage} />;
};
```

### **3. Tracking + Rôles**
```typescript
// Tracking avec permissions spécifiques
const TrackingComponent = ({ deliveryId }: { deliveryId: string }) => {
  const { user, isLivreur, isClient, isAuthenticated } = useAuth();
  const { currentLocation, startTracking, stopTracking } = useTracking(deliveryId);

  // Permissions basées sur le rôle
  const permissions = useMemo(() => ({
    canStartTracking: isLivreur && isDeliveryAssignedToUser(deliveryId, user?.id),
    canViewTracking: isClient || isLivreur,
    canControlTracking: isLivreur,
  }), [isLivreur, isClient, user, deliveryId]);

  useEffect(() => {
    if (permissions.canStartTracking) {
      // Auto-démarrer le tracking pour les livreurs
      startTracking();
      
      // Tracker l'événement analytics
      analyticsService.trackEvent(
        user.id,
        'delivery_tracking_started',
        { delivery_id: deliveryId, user_role: user.role }
      );
    }
  }, [permissions.canStartTracking]);

  if (!permissions.canViewTracking) {
    return <AccessDenied message="Accès au tracking non autorisé" />;
  }

  return (
    <View>
      <MapView>
        {/* Affichage de la carte */}
      </MapView>
      
      {permissions.canControlTracking && (
        <TrackingControls 
          onStart={startTracking}
          onStop={stopTracking}
          isActive={!!currentLocation}
        />
      )}
    </View>
  );
};
```

### **4. Analytics + Filtrage par Rôle**
```typescript
// Analytics avec données filtrées par rôle
const AnalyticsComponent = () => {
  const { user, isClient, isLivreur, isMarchand, isAdmin } = useAuth();
  const { data, loading, period, setPeriod } = useAnalytics();

  // Configuration des analytics basée sur le rôle
  const analyticsConfig = useMemo(() => {
    if (!user) return null;

    switch (user.role) {
      case 'client':
        return {
          title: 'Mes Statistiques',
          metrics: data?.clientStats,
          allowedPeriods: ['week', 'month'],
          charts: ['orders', 'spending'],
        };
      
      case 'livreur':
        return {
          title: 'Performance Livreur',
          metrics: data?.deliveryPersonStats,
          allowedPeriods: ['day', 'week', 'month'],
          charts: ['deliveries', 'earnings', 'rating'],
        };
      
      case 'marchand':
        return {
          title: 'Analytics Restaurant',
          metrics: data?.merchantStats,
          allowedPeriods: ['day', 'week', 'month', 'year'],
          charts: ['orders', 'revenue', 'products', 'ratings'],
        };
      
      case 'admin':
        return {
          title: 'Analytics Globales',
          metrics: data,
          allowedPeriods: ['day', 'week', 'month', 'year'],
          charts: ['all'],
        };
      
      default:
        return null;
    }
  }, [user, data]);

  if (!analyticsConfig) {
    return <AccessDenied message="Accès aux analytics non autorisé" />;
  }

  return (
    <AnalyticsDashboard 
      title={analyticsConfig.title}
      data={analyticsConfig.metrics}
      allowedPeriods={analyticsConfig.allowedPeriods}
      availableCharts={analyticsConfig.charts}
      userRole={user.role}
    />
  );
};
```

## 🔒 **Sécurité et Bonnes Pratiques**

### **1. Politiques RLS Complètes**
```sql
-- Notifications : accès utilisateur uniquement
CREATE POLICY "notifications_user_access" ON notifications
  FOR ALL USING (auth.uid() = user_id);

-- Chat : participants uniquement
CREATE POLICY "chat_participants_only" ON chat_messages
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM chat_participants 
      WHERE delivery_id = chat_messages.delivery_id 
      AND user_id = auth.uid()
    )
  );

-- Analytics : accès basé sur le rôle
CREATE POLICY "analytics_role_based" ON analytics_events
  FOR SELECT USING (
    user_id = auth.uid() OR 
    EXISTS (
      SELECT 1 FROM users 
      WHERE id = auth.uid() 
      AND role IN ('admin', 'marchand')
    )
  );

-- Tracking : livreurs et clients concernés
CREATE POLICY "tracking_authorized_users" ON tracking_history
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM livraisons 
      WHERE id = tracking_history.delivery_id 
      AND (client_id = auth.uid() OR livreur_id = auth.uid())
    )
  );
```

### **2. Validation et Sanitisation**
```typescript
// Middleware de validation
const validateUserInput = (data: any, schema: any) => {
  // Utiliser une bibliothèque comme Joi ou Yup
  const { error, value } = schema.validate(data);
  if (error) throw new Error(`Validation error: ${error.message}`);
  return value;
};

// Exemple d'utilisation
const updateUserProfile = async (userId: string, updates: any) => {
  // Vérifier l'authentification
  const { data: { user } } = await supabase.auth.getUser();
  if (!user || user.id !== userId) {
    throw new Error('Unauthorized');
  }

  // Valider les données
  const validatedUpdates = validateUserInput(updates, profileUpdateSchema);

  // Mettre à jour avec les données validées
  return await supabase
    .from('users')
    .update(validatedUpdates)
    .eq('id', userId);
};
```

### **3. Gestion des Sessions**
```typescript
// Configuration des timeouts de session
const SESSION_CONFIG = {
  maxIdleTime: 30 * 60 * 1000, // 30 minutes
  refreshThreshold: 5 * 60 * 1000, // 5 minutes avant expiration
  maxSessionDuration: 24 * 60 * 60 * 1000, // 24 heures
};

// Gestionnaire de session
const SessionManager = {
  lastActivity: Date.now(),
  
  updateActivity() {
    this.lastActivity = Date.now();
  },
  
  async checkSession() {
    const { data: { session } } = await supabase.auth.getSession();
    
    if (!session) return false;
    
    const now = Date.now();
    const idleTime = now - this.lastActivity;
    const sessionAge = now - new Date(session.created_at).getTime();
    
    // Vérifier les timeouts
    if (idleTime > SESSION_CONFIG.maxIdleTime || 
        sessionAge > SESSION_CONFIG.maxSessionDuration) {
      await supabase.auth.signOut();
      return false;
    }
    
    // Rafraîchir si nécessaire
    const expiresAt = new Date(session.expires_at!).getTime();
    if (expiresAt - now < SESSION_CONFIG.refreshThreshold) {
      await supabase.auth.refreshSession();
    }
    
    return true;
  },
};
```

## 🧪 **Tests d'Authentification**

### **Tests Unitaires**
```typescript
// Test du hook useAuth
describe('useAuth Hook', () => {
  test('should initialize with loading state', () => {
    const { result } = renderHook(() => useAuth());
    expect(result.current.loading).toBe(true);
    expect(result.current.user).toBe(null);
  });

  test('should handle successful login', async () => {
    const { result } = renderHook(() => useAuth());
    
    await act(async () => {
      await result.current.signIn('<EMAIL>', 'password');
    });
    
    expect(result.current.isAuthenticated).toBe(true);
    expect(result.current.user).toBeTruthy();
  });
});
```

### **Tests d'Intégration**
```typescript
// Test des permissions
describe('Role-based Access', () => {
  test('client should access client features only', async () => {
    const clientUser = { id: '1', role: 'client' };
    
    // Test accès aux commandes
    expect(canCreateOrder(clientUser)).toBe(true);
    expect(canAcceptDelivery(clientUser)).toBe(false);
    expect(canManageProducts(clientUser)).toBe(false);
  });
});
```

## 📞 **Support et Dépannage**

### **Problèmes Courants**
1. **Token expiré** : Vérifier la configuration de refresh automatique
2. **Permissions refusées** : Vérifier les politiques RLS
3. **Notifications non reçues** : Vérifier l'enregistrement des tokens push
4. **Chat inaccessible** : Vérifier les participants de la livraison

### **Logs de Débogage**
```typescript
// Activer les logs détaillés
const DEBUG_AUTH = __DEV__;

if (DEBUG_AUTH) {
  console.log('Auth state:', { user, isAuthenticated, loading });
  console.log('Permissions:', { isClient, isLivreur, isMarchand, isAdmin });
}
```

---

**🔐 Ce guide couvre l'intégration complète de l'authentification avec toutes les fonctionnalités avancées de l'application Livraison Afrique.**
