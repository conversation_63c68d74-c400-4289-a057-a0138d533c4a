# 🚀 Configuration des Fonctionnalités Avancées - Liv<PERSON>son Afrique

## 📋 Résumé de l'implémentation

### ✅ **Fonctionnalités implémentées :**

#### 1. **Hooks Avancés** (4/4 ✅)
- `useNotifications` - Gestion complète des notifications
- `useTracking` - Suivi GPS temps réel
- `useChat` - Communication temps réel
- `useAnalytics` - Métriques et analytics

#### 2. **Services Backend** (5/5 ✅)
- Service notifications push avec Expo
- Service chat temps réel
- Service tracking GPS
- Service analytics avancé
- Service Supabase étendu

#### 3. **Écrans Fonctionnels** (4/4 ✅)
- `NotificationsScreen` - Interface notifications
- `ChatScreen` - Chat temps réel
- `TrackingScreen` - Suivi avec carte
- `AnalyticsScreen` - Tableaux de bord

#### 4. **Base de Données** (1/1 ✅)
- Script SQL complet pour toutes les tables

## 🛠️ Installation des Dépendances

### 1. **Dépendances principales**
```bash
npm install expo-notifications expo-device expo-constants
npm install react-native-maps react-native-chart-kit
npm install @react-native-async-storage/async-storage
```

### 2. **Dépendances de développement**
```bash
npm install --save-dev @types/react-native-maps
```

### 3. **Configuration Expo (app.json)**
```json
{
  "expo": {
    "plugins": [
      [
        "expo-notifications",
        {
          "icon": "./assets/notification-icon.png",
          "color": "#ffffff",
          "sounds": ["./assets/notification-sound.wav"]
        }
      ],
      [
        "expo-location",
        {
          "locationAlwaysAndWhenInUsePermission": "Cette app a besoin d'accéder à votre position pour le suivi des livraisons."
        }
      ]
    ],
    "android": {
      "permissions": [
        "ACCESS_FINE_LOCATION",
        "ACCESS_COARSE_LOCATION",
        "RECEIVE_BOOT_COMPLETED",
        "VIBRATE"
      ]
    },
    "ios": {
      "infoPlist": {
        "NSLocationAlwaysAndWhenInUseUsageDescription": "Cette app a besoin d'accéder à votre position pour le suivi des livraisons.",
        "NSLocationWhenInUseUsageDescription": "Cette app a besoin d'accéder à votre position pour le suivi des livraisons."
      }
    }
  }
}
```

## 🗄️ Configuration Base de Données

### 1. **Exécuter le script SQL**
```sql
-- Copier et exécuter le contenu de database/advanced_tables.sql dans Supabase SQL Editor
```

### 2. **Configurer les politiques RLS**
Les politiques de sécurité sont incluses dans le script SQL.

### 3. **Activer Realtime**
```sql
-- Dans Supabase Dashboard > Settings > API
-- Activer Realtime pour les tables :
ALTER PUBLICATION supabase_realtime ADD TABLE chat_messages;
ALTER PUBLICATION supabase_realtime ADD TABLE notifications;
ALTER PUBLICATION supabase_realtime ADD TABLE livraisons;
```

## 🔐 Configuration de l'Authentification

### 1. **Configuration Supabase Auth**
```sql
-- Dans Supabase SQL Editor, vérifier que la table users existe
CREATE TABLE IF NOT EXISTS users (
  id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  email TEXT UNIQUE NOT NULL,
  full_name TEXT,
  phone TEXT,
  role TEXT NOT NULL CHECK (role IN ('client', 'livreur', 'marchand', 'admin')),
  avatar_url TEXT,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Activer RLS sur la table users
ALTER TABLE users ENABLE ROW LEVEL SECURITY;

-- Politique pour permettre aux utilisateurs de voir leur propre profil
CREATE POLICY "Users can view own profile" ON users
  FOR SELECT USING (auth.uid() = id);

-- Politique pour permettre aux utilisateurs de mettre à jour leur profil
CREATE POLICY "Users can update own profile" ON users
  FOR UPDATE USING (auth.uid() = id);
```

### 2. **Configuration Supabase Dashboard**
Dans votre projet Supabase :
1. **Authentication > Settings** :
   - ✅ Enable email confirmations
   - ✅ Enable password recovery
   - ✅ Set site URL: `exp://your-expo-app`
   - ✅ Add redirect URLs pour production

2. **Authentication > Email Templates** :
   - Personnaliser les templates de confirmation
   - Configurer les templates de récupération de mot de passe

3. **Authentication > Providers** :
   - Configurer les providers sociaux si nécessaire (Google, Facebook, etc.)

### 3. **Variables d'environnement (.env)**
```env
EXPO_PUBLIC_SUPABASE_URL=your_supabase_url
EXPO_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
EXPO_PUBLIC_GOOGLE_MAPS_API_KEY=your_google_maps_api_key

# Optionnel : pour les providers sociaux
EXPO_PUBLIC_GOOGLE_CLIENT_ID=your_google_client_id
EXPO_PUBLIC_FACEBOOK_APP_ID=your_facebook_app_id
```

### 4. **Configuration Google Maps**
- Activer Google Maps SDK dans Google Cloud Console
- Ajouter la clé API dans app.json

### 5. **Configuration Expo Push Notifications**
- Créer un projet EAS (Expo Application Services)
- Configurer les credentials push

## 🔧 Configuration des Services

## 🔐 Intégration Authentification + Fonctionnalités Avancées

### 1. **Initialisation complète dans App.tsx**
```typescript
import React, { useEffect } from 'react';
import { NotificationService } from './src/services/notificationService';
import { useAuth } from './src/hooks/useAuth';
import { analyticsService } from './src/services/supabase';

export default function App() {
  const { user, loading, initialize } = useAuth();

  useEffect(() => {
    // Initialiser l'authentification au démarrage
    initialize();
  }, []);

  useEffect(() => {
    if (user) {
      // 1. Initialiser les notifications push avec l'ID utilisateur
      NotificationService.initialize(user.id);

      // 2. Tracker la connexion utilisateur
      analyticsService.trackUserLogin(user.id, 'app_start');

      // 3. Mettre à jour le statut en ligne
      updateUserLastSeen(user.id);
    }
  }, [user]);

  const updateUserLastSeen = async (userId: string) => {
    try {
      await supabase
        .from('users')
        .update({ last_seen: new Date().toISOString() })
        .eq('id', userId);
    } catch (error) {
      console.error('Erreur mise à jour last_seen:', error);
    }
  };

  if (loading) {
    return <LoadingScreen />;
  }

  return user ? <AuthenticatedApp /> : <AuthenticationFlow />;
}
```

### 2. **Hook useAuth - Utilisation complète**
```typescript
import { useAuth } from './src/hooks/useAuth';

const MyComponent = () => {
  const {
    user,              // Utilisateur connecté
    loading,           // État de chargement
    error,             // Erreur d'authentification
    isAuthenticated,   // Booléen de connexion
    isClient,          // Rôle client
    isLivreur,         // Rôle livreur
    isMarchand,        // Rôle marchand
    isAdmin,           // Rôle admin
    signIn,            // Fonction de connexion
    signUp,            // Fonction d'inscription
    signOut,           // Fonction de déconnexion
    updateProfile,     // Mise à jour du profil
    resetPassword,     // Réinitialisation mot de passe
  } = useAuth();

  // Utilisation basée sur le rôle
  if (isClient) {
    // Interface client
  } else if (isLivreur) {
    // Interface livreur
  } else if (isMarchand) {
    // Interface marchand
  }
};
```

### 3. **Gestion des rôles et permissions**
```typescript
// Composant de protection par rôle
const RoleProtectedComponent = ({
  allowedRoles,
  children
}: {
  allowedRoles: string[],
  children: React.ReactNode
}) => {
  const { user, isAuthenticated } = useAuth();

  if (!isAuthenticated || !user) {
    return <LoginRequired />;
  }

  if (!allowedRoles.includes(user.role)) {
    return <AccessDenied />;
  }

  return <>{children}</>;
};

// Utilisation
<RoleProtectedComponent allowedRoles={['livreur', 'admin']}>
  <TrackingScreen />
</RoleProtectedComponent>
```

## 📱 Intégration dans l'App

### 2. **Navigation**
Ajouter les nouveaux écrans dans votre navigateur :
```typescript
// Dans votre Stack Navigator
<Stack.Screen name="Notifications" component={NotificationsScreen} />
<Stack.Screen name="Chat" component={ChatScreen} />
<Stack.Screen name="Tracking" component={TrackingScreen} />
<Stack.Screen name="Analytics" component={AnalyticsScreen} />
```

### 3. **Permissions**
```typescript
// Demander les permissions au démarrage
import * as Location from 'expo-location';

const requestPermissions = async () => {
  // Permissions de localisation
  await Location.requestForegroundPermissionsAsync();
  await Location.requestBackgroundPermissionsAsync();
  
  // Permissions de notifications
  await NotificationService.requestPermissions();
};
```

## 🔐 Authentification et Fonctionnalités Avancées

### 1. **Notifications Push et Authentification**
```typescript
// Les notifications sont liées à l'utilisateur connecté
const NotificationsComponent = () => {
  const { user, isAuthenticated } = useAuth();
  const { notifications, unreadCount } = useNotifications();

  useEffect(() => {
    if (isAuthenticated && user) {
      // Enregistrer le token push pour cet utilisateur
      NotificationService.initialize(user.id);
    }
  }, [isAuthenticated, user]);

  // Les notifications sont automatiquement filtrées par user_id
  return (
    <View>
      {notifications.map(notification => (
        <NotificationItem
          key={notification.id}
          notification={notification}
          onPress={() => handleNotificationPress(notification)}
        />
      ))}
    </View>
  );
};
```

### 2. **Chat et Permissions par Rôle**
```typescript
const ChatComponent = ({ deliveryId }: { deliveryId: string }) => {
  const { user, isClient, isLivreur, isMarchand } = useAuth();
  const { messages, participants, sendMessage } = useChat(deliveryId);

  // Vérifier les permissions d'accès au chat
  const canAccessChat = useMemo(() => {
    if (!user) return false;

    // Vérifier si l'utilisateur est participant à cette livraison
    return participants.some(p => p.user_id === user.id);
  }, [user, participants]);

  const handleSendMessage = async (content: string) => {
    if (!user || !canAccessChat) return;

    await sendMessage(content, 'text');

    // Notifier les autres participants
    const otherParticipants = participants.filter(p => p.user_id !== user.id);
    for (const participant of otherParticipants) {
      await NotificationService.notifyNewMessage(
        participant.user_id,
        user.full_name,
        content,
        deliveryId
      );
    }
  };

  if (!canAccessChat) {
    return <AccessDenied message="Vous n'avez pas accès à ce chat" />;
  }

  return <ChatInterface />;
};
```

### 3. **Tracking et Rôles Spécifiques**
```typescript
const TrackingComponent = ({ deliveryId }: { deliveryId: string }) => {
  const { user, isLivreur, isClient } = useAuth();
  const { currentLocation, startTracking, stopTracking } = useTracking(deliveryId);

  // Seuls les livreurs peuvent démarrer le tracking
  const canStartTracking = isLivreur;

  // Clients et livreurs peuvent voir le tracking
  const canViewTracking = isClient || isLivreur;

  useEffect(() => {
    if (isLivreur && user) {
      // Auto-démarrer le tracking pour les livreurs
      startTracking();

      // Tracker l'événement analytics
      analyticsService.trackEvent(
        user.id,
        'delivery_tracking_started',
        { delivery_id: deliveryId }
      );
    }
  }, [isLivreur, user, deliveryId]);

  if (!canViewTracking) {
    return <AccessDenied />;
  }

  return (
    <View>
      <MapView>
        {/* Affichage de la carte */}
      </MapView>

      {canStartTracking && (
        <TrackingControls
          onStart={startTracking}
          onStop={stopTracking}
        />
      )}
    </View>
  );
};
```

### 4. **Analytics Basées sur les Rôles**
```typescript
const AnalyticsComponent = () => {
  const { user, isClient, isLivreur, isMarchand, isAdmin } = useAuth();
  const { data, loading, period, setPeriod } = useAnalytics();

  // Données différentes selon le rôle
  const getAnalyticsData = () => {
    if (!user) return null;

    if (isClient) {
      return {
        title: 'Mes Statistiques',
        metrics: data?.clientStats,
        allowedPeriods: ['week', 'month'],
      };
    } else if (isLivreur) {
      return {
        title: 'Performance Livreur',
        metrics: data?.deliveryPersonStats,
        allowedPeriods: ['day', 'week', 'month'],
      };
    } else if (isMarchand) {
      return {
        title: 'Analytics Restaurant',
        metrics: data?.merchantStats,
        allowedPeriods: ['day', 'week', 'month', 'year'],
      };
    } else if (isAdmin) {
      return {
        title: 'Analytics Globales',
        metrics: data,
        allowedPeriods: ['day', 'week', 'month', 'year'],
      };
    }

    return null;
  };

  const analyticsConfig = getAnalyticsData();

  if (!analyticsConfig) {
    return <AccessDenied />;
  }

  return (
    <AnalyticsDashboard
      title={analyticsConfig.title}
      data={analyticsConfig.metrics}
      allowedPeriods={analyticsConfig.allowedPeriods}
    />
  );
};
```

## 🎯 Utilisation des Hooks

### 1. **Hook useNotifications**
```typescript
const {
  notifications,
  unreadCount,
  markAsRead,
  refreshNotifications
} = useNotifications();
```

### 2. **Hook useTracking**
```typescript
const { 
  currentLocation, 
  trackingHistory, 
  startTracking, 
  stopTracking 
} = useTracking(deliveryId);
```

### 3. **Hook useChat**
```typescript
const { 
  messages, 
  participants, 
  sendMessage, 
  markAsRead 
} = useChat(deliveryId);
```

### 4. **Hook useAnalytics**
```typescript
const { 
  data, 
  loading, 
  period, 
  setPeriod, 
  refresh 
} = useAnalytics();
```

## 🔄 Fonctionnalités Temps Réel

### 1. **Notifications Push**
```typescript
// Envoyer une notification
await NotificationService.notifyOrderStatusChange(
  userId, 
  orderId, 
  'en_preparation', 
  'Restaurant Délice'
);
```

### 2. **Chat Temps Réel**
```typescript
// Envoyer un message
await chatService.sendMessage(
  chatId, 
  deliveryId, 
  senderId, 
  'Message de test'
);
```

### 3. **Tracking GPS**
```typescript
// Mettre à jour la position
await trackingService.updateDeliveryLocation(
  deliveryId, 
  latitude, 
  longitude, 
  estimatedArrival
);
```

## 📊 Analytics et Métriques

### 1. **Événements Analytics**
```typescript
// Tracker un événement
await analyticsService.trackEvent(
  userId, 
  'order_placed', 
  { orderId, amount: 15000 }
);
```

### 2. **Métriques par Rôle**
- **Client** : Commandes, dépenses, restaurants favoris
- **Livreur** : Livraisons, gains, performance
- **Marchand** : Ventes, produits populaires, revenus

## 🔒 Sécurité et Bonnes Pratiques d'Authentification

### 1. **Politiques RLS (Row Level Security)**
```sql
-- Exemple de politiques RLS pour les notifications
CREATE POLICY "Users can only see their own notifications" ON notifications
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can only update their own notifications" ON notifications
  FOR UPDATE USING (auth.uid() = user_id);

-- Politique pour le chat - seuls les participants peuvent voir les messages
CREATE POLICY "Chat participants can view messages" ON chat_messages
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM chat_participants
      WHERE (chat_id = chat_messages.chat_id OR delivery_id = chat_messages.delivery_id)
      AND user_id = auth.uid()
    )
  );

-- Politique pour les analytics - accès basé sur le rôle
CREATE POLICY "Role-based analytics access" ON analytics_events
  FOR SELECT USING (
    user_id = auth.uid() OR
    EXISTS (
      SELECT 1 FROM users
      WHERE id = auth.uid()
      AND role IN ('admin', 'marchand')
    )
  );
```

### 2. **Validation des Permissions Côté Client**
```typescript
// Hook pour vérifier les permissions
const usePermissions = () => {
  const { user } = useAuth();

  const canAccessChat = (deliveryId: string) => {
    if (!user) return false;
    // Vérifier si l'utilisateur est impliqué dans cette livraison
    return checkDeliveryParticipation(user.id, deliveryId);
  };

  const canStartTracking = (deliveryId: string) => {
    if (!user || user.role !== 'livreur') return false;
    // Vérifier si le livreur est assigné à cette livraison
    return checkDeliveryAssignment(user.id, deliveryId);
  };

  const canViewAnalytics = (scope: 'own' | 'global') => {
    if (!user) return false;
    if (scope === 'own') return true;
    return ['admin', 'marchand'].includes(user.role);
  };

  return {
    canAccessChat,
    canStartTracking,
    canViewAnalytics,
  };
};
```

### 3. **Gestion Sécurisée des Tokens Push**
```typescript
// Service sécurisé pour les tokens push
const SecurePushTokenService = {
  async registerToken(userId: string, token: string) {
    try {
      // Vérifier que l'utilisateur est authentifié
      const { data: { user } } = await supabase.auth.getUser();
      if (!user || user.id !== userId) {
        throw new Error('Unauthorized token registration');
      }

      // Enregistrer le token avec validation
      const { error } = await supabase
        .from('push_tokens')
        .upsert({
          user_id: userId,
          token,
          platform: Platform.OS,
          is_active: true,
          updated_at: new Date().toISOString(),
        });

      if (error) throw error;
    } catch (error) {
      console.error('Erreur sécurisée token push:', error);
      throw error;
    }
  },

  async deactivateOldTokens(userId: string, currentToken: string) {
    // Désactiver les anciens tokens pour éviter les doublons
    const { error } = await supabase
      .from('push_tokens')
      .update({ is_active: false })
      .eq('user_id', userId)
      .neq('token', currentToken);

    if (error) console.error('Erreur désactivation tokens:', error);
  },
};
```

### 4. **Validation des Données Sensibles**
```typescript
// Middleware de validation pour les données sensibles
const validateUserData = (data: any, allowedFields: string[]) => {
  const sanitizedData: any = {};

  for (const field of allowedFields) {
    if (data[field] !== undefined) {
      sanitizedData[field] = data[field];
    }
  }

  return sanitizedData;
};

// Utilisation dans les services
const updateUserProfile = async (userId: string, updates: any) => {
  // Champs autorisés pour la mise à jour
  const allowedFields = ['full_name', 'phone', 'avatar_url'];
  const sanitizedUpdates = validateUserData(updates, allowedFields);

  // Vérifier que l'utilisateur ne peut modifier que son propre profil
  const { data: { user } } = await supabase.auth.getUser();
  if (!user || user.id !== userId) {
    throw new Error('Unauthorized profile update');
  }

  return await supabase
    .from('users')
    .update(sanitizedUpdates)
    .eq('id', userId);
};
```

### 5. **Gestion des Sessions et Timeouts**
```typescript
// Configuration des timeouts de session
const SESSION_CONFIG = {
  maxIdleTime: 30 * 60 * 1000, // 30 minutes
  refreshThreshold: 5 * 60 * 1000, // 5 minutes avant expiration
};

const SessionManager = {
  lastActivity: Date.now(),

  updateActivity() {
    this.lastActivity = Date.now();
  },

  async checkSession() {
    const { data: { session } } = await supabase.auth.getSession();

    if (!session) return false;

    const now = Date.now();
    const sessionAge = now - new Date(session.created_at).getTime();
    const idleTime = now - this.lastActivity;

    // Vérifier le timeout d'inactivité
    if (idleTime > SESSION_CONFIG.maxIdleTime) {
      await supabase.auth.signOut();
      return false;
    }

    // Rafraîchir le token si nécessaire
    const expiresAt = new Date(session.expires_at!).getTime();
    if (expiresAt - now < SESSION_CONFIG.refreshThreshold) {
      await supabase.auth.refreshSession();
    }

    return true;
  },
};
```

## 🚨 Points d'Attention

### 1. **Performance**
- Utiliser la pagination pour les listes
- Implémenter le cache pour les données fréquentes
- Optimiser les requêtes Supabase
- Débouncer les requêtes de recherche
- Utiliser React.memo pour les composants lourds

### 2. **Sécurité Avancée**
- ✅ Politiques RLS sur toutes les tables
- ✅ Validation des permissions côté client ET serveur
- ✅ Chiffrement des données sensibles
- ✅ Gestion sécurisée des tokens push
- ✅ Timeouts de session configurables
- ✅ Audit trail des actions sensibles
- ✅ Protection contre les attaques CSRF
- ✅ Validation et sanitisation des inputs

### 3. **UX/UI et Accessibilité**
- Gérer les états de chargement avec des skeletons
- Implémenter les états d'erreur avec retry
- Optimiser pour les connexions lentes
- Support des lecteurs d'écran
- Contraste et tailles de police accessibles
- Navigation au clavier

## 🧪 Tests

### 1. **Tests des Services**
```bash
npm test -- --testPathPattern=services
```

### 2. **Tests des Hooks**
```bash
npm test -- --testPathPattern=hooks
```

### 3. **Tests d'Intégration**
```bash
npm test -- --testPathPattern=integration
```

## 📈 Prochaines Étapes

### 1. **Optimisations**
- Cache intelligent
- Compression des images
- Optimisation des requêtes

### 2. **Nouvelles Fonctionnalités**
- Paiement en ligne
- Programme de fidélité
- IA pour recommandations

### 3. **Monitoring**
- Crashlytics
- Performance monitoring
- Analytics avancées

## 🆘 Support

Pour toute question ou problème :
1. Vérifier la documentation Supabase
2. Consulter la documentation Expo
3. Tester sur un appareil physique
4. Vérifier les logs de développement

---

**🎉 Félicitations ! Votre application dispose maintenant de toutes les fonctionnalités avancées pour une expérience de livraison complète et moderne.**
