// Configuration spécifique pour react-native-gesture-handler
// Ce fichier doit être importé avant toute autre chose

import 'react-native-gesture-handler';

// Forcer l'initialisation du gesture handler
if (typeof global !== 'undefined') {
  // S'assurer que les globals nécessaires sont disponibles
  global.__reanimatedWorkletInit = global.__reanimatedWorkletInit || function() {};
  
  // Initialiser les gestes de manière explicite
  try {
    const { gestureHandlerRootHOC } = require('react-native-gesture-handler');
    if (gestureHandlerRootHOC) {
      console.log('Gesture Handler initialisé avec succès');
    }
  } catch (error) {
    console.warn('Erreur lors de l\'initialisation de Gesture Handler:', error);
  }
}

console.log('Configuration Gesture Handler chargée');
