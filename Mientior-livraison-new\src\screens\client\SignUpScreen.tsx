import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  ScrollView,
  StatusBar,
  Animated,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { theme } from '../../constants/theme';
import { useAuth } from '../../hooks/useAuth';
import { RootStackParamList } from '../../types';

interface FormData {
  firstName: string;
  lastName: string;
  phone: string;
  email: string;
  password: string;
  confirmPassword: string;
}

interface FormErrors {
  firstName?: string;
  lastName?: string;
  phone?: string;
  email?: string;
  password?: string;
  confirmPassword?: string;
}

type SignUpScreenNavigationProp = StackNavigationProp<RootStackParamList, 'SignUpScreen'>;

export const SignUpScreen: React.FC = () => {
  const navigation = useNavigation<SignUpScreenNavigationProp>();
  const { signUp, loading, error } = useAuth();
  const [formData, setFormData] = useState<FormData>({
    firstName: '',
    lastName: '',
    phone: '',
    email: '',
    password: '',
    confirmPassword: '',
  });
  const [errors, setErrors] = useState<FormErrors>({});
  const [acceptTerms, setAcceptTerms] = useState(false);
  const [acceptNewsletter, setAcceptNewsletter] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(30)).current;

  React.useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 800,
        useNativeDriver: true,
      }),
    ]).start();
  }, [fadeAnim, slideAnim]);

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    // Validation prénom
    if (!formData.firstName.trim()) {
      newErrors.firstName = 'Le prénom est requis';
    } else if (formData.firstName.trim().length < 2) {
      newErrors.firstName = 'Le prénom doit contenir au moins 2 caractères';
    }

    // Validation nom
    if (!formData.lastName.trim()) {
      newErrors.lastName = 'Le nom est requis';
    } else if (formData.lastName.trim().length < 2) {
      newErrors.lastName = 'Le nom doit contenir au moins 2 caractères';
    }

    // Validation téléphone
    const phoneRegex = /^(\+[1-9]\d{1,14}|0[1-9]\d{8,9})$/;
    if (!formData.phone.trim()) {
      newErrors.phone = 'Le numéro de téléphone est requis';
    } else if (!phoneRegex.test(formData.phone.replace(/\s/g, ''))) {
      newErrors.phone = 'Format de téléphone invalide';
    }

    // Validation email
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!formData.email.trim()) {
      newErrors.email = 'L\'email est requis';
    } else if (!emailRegex.test(formData.email)) {
      newErrors.email = 'Format d\'email invalide';
    }

    // Validation mot de passe
    if (!formData.password) {
      newErrors.password = 'Le mot de passe est requis';
    } else if (formData.password.length < 8) {
      newErrors.password = 'Le mot de passe doit contenir au moins 8 caractères';
    } else if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(formData.password)) {
      newErrors.password = 'Le mot de passe doit contenir au moins une majuscule, une minuscule et un chiffre';
    }

    // Validation confirmation mot de passe
    if (!formData.confirmPassword) {
      newErrors.confirmPassword = 'La confirmation du mot de passe est requise';
    } else if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = 'Les mots de passe ne correspondent pas';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSignUp = async () => {
    if (!validateForm()) {
      return;
    }

    if (!acceptTerms) {
      Alert.alert('Erreur', 'Vous devez accepter les conditions d\'utilisation pour continuer.');
      return;
    }

    try {
      // Inscription avec le hook useAuth
      const result = await signUp(formData.email, formData.password, {
        full_name: `${formData.firstName} ${formData.lastName}`,
        phone: formData.phone,
        role: 'client',
        // Retirer accept_newsletter car il n'existe pas dans le type User
      });

      if (result?.user) {
        // Rediriger vers l'écran de vérification OTP
        navigation.navigate('OTPVerificationScreen', {
          email: formData.email,
          phone: formData.phone,
        });
      }
    } catch (error: any) {
      console.error('Erreur d\'inscription:', error);
      Alert.alert(
        'Erreur d\'inscription',
        error.message || 'Une erreur est survenue lors de l\'inscription. Veuillez réessayer.'
      );
    }
  };

  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Effacer l'erreur du champ modifié
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const renderInput = (
    field: keyof FormData,
    placeholder: string,
    icon: string,
    keyboardType: any = 'default'
  ) => {
    const isPasswordField = field === 'password' || field === 'confirmPassword';
    const showPasswordState = field === 'password' ? showPassword : showConfirmPassword;
    const setShowPasswordState = field === 'password' ? setShowPassword : setShowConfirmPassword;

    return (
      <View style={styles.inputContainer}>
        <View style={[styles.inputWrapper, errors[field] && styles.inputError]}>
          <Text style={styles.inputIcon}>{icon}</Text>
          <TextInput
            style={styles.textInput}
            placeholder={placeholder}
            placeholderTextColor={theme.colors.textSecondary}
            value={formData[field]}
            onChangeText={(value) => handleInputChange(field, value)}
            keyboardType={keyboardType}
            secureTextEntry={isPasswordField && !showPasswordState}
            autoCapitalize={isPasswordField ? 'none' : 'words'}
            autoCorrect={false}
          />
          {isPasswordField && (
            <TouchableOpacity
              style={styles.passwordToggle}
              onPress={() => setShowPasswordState(!showPasswordState)}
            >
              <Text style={styles.passwordToggleText}>
                {showPasswordState ? '🙈' : '👁️'}
              </Text>
            </TouchableOpacity>
          )}
        </View>
        {errors[field] && (
          <Text style={styles.errorText}>{errors[field]}</Text>
        )}
      </View>
    );
  };

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <StatusBar backgroundColor={theme.colors.white} barStyle="dark-content" />
      
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
      >
        <Animated.View
          style={[
            styles.content,
            {
              opacity: fadeAnim,
              transform: [{ translateY: slideAnim }],
            },
          ]}
        >
          {/* Header */}
          <View style={styles.header}>
            <TouchableOpacity
              style={styles.backButton}
              onPress={() => navigation.goBack()}
            >
              <Text style={styles.backButtonText}>←</Text>
            </TouchableOpacity>
            <Text style={styles.title}>Créer un compte</Text>
            <Text style={styles.subtitle}>
              Rejoignez des milliers d'utilisateurs qui font confiance à Mientior
            </Text>
          </View>

          {/* Affichage des erreurs globales */}
          {error && (
            <View style={styles.errorContainer}>
              <Text style={styles.errorText}>{error}</Text>
            </View>
          )}

          {/* Formulaire */}
          <View style={styles.form}>
            {renderInput('firstName', 'Prénom', '👤')}
            {renderInput('lastName', 'Nom de famille', '👤')}
            {renderInput('phone', 'Numéro de téléphone', '📱', 'phone-pad')}
            {renderInput('email', 'Adresse email', '📧', 'email-address')}
            {renderInput('password', 'Mot de passe', '🔒', 'default')}
            {renderInput('confirmPassword', 'Confirmer le mot de passe', '🔒', 'default')}

            {/* Indicateur de force du mot de passe */}
            {formData.password.length > 0 && (
              <View style={styles.passwordStrength}>
                <Text style={styles.passwordStrengthLabel}>Force du mot de passe :</Text>
                <View style={styles.passwordStrengthBar}>
                  <View
                    style={[
                      styles.passwordStrengthFill,
                      {
                        width: `${Math.min(
                          (formData.password.length / 12) * 100,
                          100
                        )}%`,
                        backgroundColor:
                          formData.password.length < 6
                            ? theme.colors.error
                            : formData.password.length < 10
                            ? '#FFA500'
                            : theme.colors.success,
                      },
                    ]}
                  />
                </View>
              </View>
            )}

            {/* Cases à cocher */}
            <View style={styles.checkboxContainer}>
              <TouchableOpacity
                style={styles.checkbox}
                onPress={() => setAcceptTerms(!acceptTerms)}
              >
                <View style={[styles.checkboxBox, acceptTerms && styles.checkboxChecked]}>
                  {acceptTerms && <Text style={styles.checkboxCheck}>✓</Text>}
                </View>
                <Text style={styles.checkboxText}>
                  J'accepte les{' '}
                  <Text style={styles.linkText}>Conditions d'utilisation</Text>
                  {' '}et la{' '}
                  <Text style={styles.linkText}>Politique de confidentialité</Text>
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.checkbox}
                onPress={() => setAcceptNewsletter(!acceptNewsletter)}
              >
                <View style={[styles.checkboxBox, acceptNewsletter && styles.checkboxChecked]}>
                  {acceptNewsletter && <Text style={styles.checkboxCheck}>✓</Text>}
                </View>
                <Text style={styles.checkboxText}>
                  Je souhaite recevoir les offres et actualités par email
                </Text>
              </TouchableOpacity>
            </View>

            {/* Bouton d'inscription */}
            <TouchableOpacity
              style={[styles.signUpButton, loading && styles.signUpButtonDisabled]}
              onPress={handleSignUp}
              disabled={loading}
            >
              <Text style={styles.signUpButtonText}>
                {loading ? 'Création en cours...' : 'Créer mon compte'}
              </Text>
            </TouchableOpacity>

            {/* Lien vers connexion */}
            <View style={styles.signInLink}>
              <Text style={styles.signInLinkText}>Vous avez déjà un compte ? </Text>
              <TouchableOpacity onPress={() => navigation.navigate('SignInScreen')}>
                <Text style={styles.signInLinkButton}>Se connecter</Text>
              </TouchableOpacity>
            </View>
          </View>
        </Animated.View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.white,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
  },
  content: {
    flex: 1,
    paddingHorizontal: 24,
  },
  header: {
    paddingTop: 60,
    paddingBottom: 40,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: theme.colors.background.secondary,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
  },
  backButtonText: {
    fontSize: 20,
    color: theme.colors.text.primary,
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    color: theme.colors.text.primary,
    marginBottom: 12,
  },
  subtitle: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    lineHeight: 24,
  },
  errorContainer: {
    backgroundColor: '#FFF5F5',
    borderColor: theme.colors.error,
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    marginBottom: 20,
  },
  form: {
    flex: 1,
    paddingBottom: 40,
  },
  inputContainer: {
    marginBottom: 20,
  },
  inputWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.background.secondary,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 4,
    borderWidth: 2,
    borderColor: 'transparent',
  },
  inputError: {
    borderColor: theme.colors.error,
  },
  inputIcon: {
    fontSize: 20,
    marginRight: 12,
  },
  textInput: {
    flex: 1,
    fontSize: 16,
    color: theme.colors.text.primary,
    paddingVertical: 16,
  },
  passwordToggle: {
    padding: 8,
  },
  passwordToggleText: {
    fontSize: 18,
  },
  errorText: {
    color: theme.colors.error,
    fontSize: 14,
    marginTop: 8,
    marginLeft: 16,
  },
  passwordStrength: {
    marginBottom: 20,
  },
  passwordStrengthLabel: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginBottom: 8,
  },
  passwordStrengthBar: {
    height: 4,
    backgroundColor: theme.colors.background.secondary,
    borderRadius: 2,
    overflow: 'hidden',
  },
  passwordStrengthFill: {
    height: '100%',
    borderRadius: 2,
  },
  checkboxContainer: {
    marginBottom: 32,
  },
  checkbox: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  checkboxBox: {
    width: 20,
    height: 20,
    borderRadius: 4,
    borderWidth: 2,
    borderColor: theme.colors.textSecondary,
    marginRight: 12,
    marginTop: 2,
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkboxChecked: {
    backgroundColor: theme.colors.primary,
    borderColor: theme.colors.primary,
  },
  checkboxCheck: {
    color: theme.colors.white,
    fontSize: 12,
    fontWeight: 'bold',
  },
  checkboxText: {
    flex: 1,
    fontSize: 14,
    color: theme.colors.text.primary,
    lineHeight: 20,
  },
  linkText: {
    color: theme.colors.primary,
    fontWeight: '500',
  },
  signUpButton: {
    backgroundColor: theme.colors.primary,
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
    marginBottom: 24,
    shadowColor: theme.colors.primary,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  signUpButtonDisabled: {
    backgroundColor: theme.colors.textSecondary,
    shadowOpacity: 0,
    elevation: 0,
  },
  signUpButtonText: {
    color: theme.colors.white,
    fontSize: 18,
    fontWeight: '600',
  },
  signInLink: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  signInLinkText: {
    fontSize: 16,
    color: theme.colors.textSecondary,
  },
  signInLinkButton: {
    fontSize: 16,
    color: theme.colors.primary,
    fontWeight: '600',
  },
});
