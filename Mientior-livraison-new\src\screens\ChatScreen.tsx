import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TextInput,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation, useRoute } from '@react-navigation/native';
import { useAuth } from '../hooks/useAuth';
import { useChat } from '../hooks/useChat';
import { chatService } from '../services/supabase';

interface ChatMessage {
  id: string;
  content: string;
  type: 'text' | 'image' | 'location' | 'system';
  sender_id: string;
  created_at: string;
  sender?: {
    id: string;
    full_name: string;
    role: string;
    avatar_url?: string;
  };
  read_by: string[];
  metadata?: any;
}

interface RouteParams {
  deliveryId: string;
  chatId?: string;
}

const ChatScreen: React.FC = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { deliveryId, chatId } = route.params as RouteParams;
  const { user } = useAuth();
  const { messages, participants, sendMessage, markAsRead } = useChat(deliveryId);
  
  const [newMessage, setNewMessage] = useState('');
  const [loading, setLoading] = useState(false);
  const [sending, setSending] = useState(false);
  const flatListRef = useRef<FlatList>(null);

  useEffect(() => {
    if (messages.length > 0 && user) {
      markAsRead(user.id);
    }
  }, [messages, user, markAsRead]);

  useEffect(() => {
    // Scroll to bottom when new messages arrive
    if (messages.length > 0) {
      setTimeout(() => {
        flatListRef.current?.scrollToEnd({ animated: true });
      }, 100);
    }
  }, [messages]);

  const handleSendMessage = async () => {
    if (!newMessage.trim() || !user || sending) return;

    const messageContent = newMessage.trim();
    setNewMessage('');
    setSending(true);

    try {
      await sendMessage(messageContent, 'text');
    } catch (error) {
      console.error('Erreur lors de l\'envoi du message:', error);
      Alert.alert('Erreur', 'Impossible d\'envoyer le message');
      setNewMessage(messageContent); // Restore message on error
    } finally {
      setSending(false);
    }
  };

  const handleLocationShare = async () => {
    if (!user) return;

    Alert.alert(
      'Partager la position',
      'Voulez-vous partager votre position actuelle ?',
      [
        { text: 'Annuler', style: 'cancel' },
        {
          text: 'Partager',
          onPress: async () => {
            try {
              setSending(true);
              // Ici, vous récupéreriez la position réelle
              const mockLocation = {
                latitude: 5.3364,
                longitude: -4.0267,
                address: 'Abidjan, Côte d\'Ivoire'
              };
              
              await sendMessage(
                `Position partagée: ${mockLocation.address}`,
                'location',
                mockLocation
              );
            } catch (error) {
              console.error('Erreur lors du partage de position:', error);
              Alert.alert('Erreur', 'Impossible de partager la position');
            } finally {
              setSending(false);
            }
          }
        }
      ]
    );
  };

  const isMyMessage = (message: ChatMessage) => {
    return message.sender_id === user?.id;
  };

  const getMessageTime = (timestamp: string) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString('fr-FR', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getUserRole = (role: string) => {
    switch (role) {
      case 'client':
        return 'Client';
      case 'livreur':
        return 'Livreur';
      case 'marchand':
        return 'Restaurant';
      case 'support':
        return 'Support';
      default:
        return role;
    }
  };

  const renderMessage = ({ item }: { item: ChatMessage }) => {
    const isMine = isMyMessage(item);
    
    return (
      <View style={[
        styles.messageContainer,
        isMine ? styles.myMessageContainer : styles.otherMessageContainer
      ]}>
        {!isMine && (
          <View style={styles.senderInfo}>
            <Text style={styles.senderName}>
              {item.sender?.full_name || 'Utilisateur'}
            </Text>
            <Text style={styles.senderRole}>
              {getUserRole(item.sender?.role || '')}
            </Text>
          </View>
        )}
        
        <View style={[
          styles.messageBubble,
          isMine ? styles.myMessageBubble : styles.otherMessageBubble
        ]}>
          {item.type === 'location' ? (
            <View style={styles.locationMessage}>
              <Ionicons name="location" size={16} color={isMine ? 'white' : '#007AFF'} />
              <Text style={[
                styles.messageText,
                isMine ? styles.myMessageText : styles.otherMessageText
              ]}>
                {item.content}
              </Text>
            </View>
          ) : item.type === 'system' ? (
            <Text style={styles.systemMessageText}>
              {item.content}
            </Text>
          ) : (
            <Text style={[
              styles.messageText,
              isMine ? styles.myMessageText : styles.otherMessageText
            ]}>
              {item.content}
            </Text>
          )}
          
          <Text style={[
            styles.messageTime,
            isMine ? styles.myMessageTime : styles.otherMessageTime
          ]}>
            {getMessageTime(item.created_at)}
          </Text>
        </View>
      </View>
    );
  };

  const renderParticipants = () => {
    if (participants.length <= 2) return null;

    return (
      <View style={styles.participantsContainer}>
        <Text style={styles.participantsTitle}>Participants:</Text>
        {participants.map((participant) => (
          <Text key={participant.user_id} style={styles.participantName}>
            {participant.user?.full_name} ({getUserRole(participant.role)})
          </Text>
        ))}
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color="#333" />
        </TouchableOpacity>
        
        <View style={styles.headerInfo}>
          <Text style={styles.headerTitle}>Chat Livraison</Text>
          <Text style={styles.headerSubtitle}>
            {participants.length} participant{participants.length > 1 ? 's' : ''}
          </Text>
        </View>
        
        <TouchableOpacity
          style={styles.infoButton}
          onPress={() => {
            Alert.alert(
              'Informations',
              `Livraison: ${deliveryId}\nParticipants: ${participants.length}`
            );
          }}
        >
          <Ionicons name="information-circle-outline" size={24} color="#007AFF" />
        </TouchableOpacity>
      </View>

      {renderParticipants()}

      <FlatList
        ref={flatListRef}
        data={messages}
        renderItem={renderMessage}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.messagesContainer}
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Ionicons name="chatbubbles-outline" size={64} color="#E0E0E0" />
            <Text style={styles.emptyText}>
              Aucun message pour le moment
            </Text>
            <Text style={styles.emptySubtext}>
              Commencez la conversation !
            </Text>
          </View>
        }
      />

      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.inputContainer}
      >
        <View style={styles.inputRow}>
          <TouchableOpacity
            style={styles.locationButton}
            onPress={handleLocationShare}
            disabled={sending}
          >
            <Ionicons name="location-outline" size={24} color="#007AFF" />
          </TouchableOpacity>
          
          <TextInput
            style={styles.textInput}
            value={newMessage}
            onChangeText={setNewMessage}
            placeholder="Tapez votre message..."
            multiline
            maxLength={500}
            editable={!sending}
          />
          
          <TouchableOpacity
            style={[
              styles.sendButton,
              (!newMessage.trim() || sending) && styles.sendButtonDisabled
            ]}
            onPress={handleSendMessage}
            disabled={!newMessage.trim() || sending}
          >
            {sending ? (
              <ActivityIndicator size="small" color="white" />
            ) : (
              <Ionicons name="send" size={20} color="white" />
            )}
          </TouchableOpacity>
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F5F5',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  backButton: {
    padding: 8,
    marginRight: 8,
  },
  headerInfo: {
    flex: 1,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  headerSubtitle: {
    fontSize: 14,
    color: '#666',
  },
  infoButton: {
    padding: 8,
  },
  participantsContainer: {
    backgroundColor: 'white',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  participantsTitle: {
    fontSize: 12,
    fontWeight: '600',
    color: '#666',
    marginBottom: 4,
  },
  participantName: {
    fontSize: 12,
    color: '#333',
  },
  messagesContainer: {
    padding: 16,
    flexGrow: 1,
  },
  messageContainer: {
    marginBottom: 16,
  },
  myMessageContainer: {
    alignItems: 'flex-end',
  },
  otherMessageContainer: {
    alignItems: 'flex-start',
  },
  senderInfo: {
    marginBottom: 4,
  },
  senderName: {
    fontSize: 12,
    fontWeight: '600',
    color: '#333',
  },
  senderRole: {
    fontSize: 10,
    color: '#666',
  },
  messageBubble: {
    maxWidth: '80%',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 16,
  },
  myMessageBubble: {
    backgroundColor: '#007AFF',
    borderBottomRightRadius: 4,
  },
  otherMessageBubble: {
    backgroundColor: 'white',
    borderBottomLeftRadius: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  messageText: {
    fontSize: 16,
    lineHeight: 20,
  },
  myMessageText: {
    color: 'white',
  },
  otherMessageText: {
    color: '#333',
  },
  systemMessageText: {
    fontSize: 14,
    color: '#666',
    fontStyle: 'italic',
    textAlign: 'center',
  },
  locationMessage: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  messageTime: {
    fontSize: 10,
    marginTop: 4,
  },
  myMessageTime: {
    color: 'rgba(255, 255, 255, 0.7)',
    textAlign: 'right',
  },
  otherMessageTime: {
    color: '#999',
  },
  inputContainer: {
    backgroundColor: 'white',
    borderTopWidth: 1,
    borderTopColor: '#E0E0E0',
  },
  inputRow: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  locationButton: {
    padding: 8,
    marginRight: 8,
  },
  textInput: {
    flex: 1,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 8,
    maxHeight: 100,
    fontSize: 16,
  },
  sendButton: {
    backgroundColor: '#007AFF',
    borderRadius: 20,
    padding: 8,
    marginLeft: 8,
    justifyContent: 'center',
    alignItems: 'center',
    width: 40,
    height: 40,
  },
  sendButtonDisabled: {
    backgroundColor: '#CCCCCC',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 64,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#999',
    marginTop: 16,
    textAlign: 'center',
  },
  emptySubtext: {
    fontSize: 14,
    color: '#CCCCCC',
    marginTop: 8,
    textAlign: 'center',
  },
});

export default ChatScreen;
