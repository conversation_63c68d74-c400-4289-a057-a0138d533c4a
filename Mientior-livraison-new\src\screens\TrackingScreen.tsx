import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert,
  Linking,
  ActivityIndicator,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation, useRoute } from '@react-navigation/native';
import MapView, { Marker, Polyline, PROVIDER_GOOGLE } from 'react-native-maps';
import { useAuth } from '../hooks/useAuth';
import { useTracking } from '../hooks/useTracking';
import { trackingService, chatService } from '../services/supabase';

interface RouteParams {
  deliveryId: string;
}

interface TrackingEvent {
  id: string;
  status: string;
  location?: {
    latitude: number;
    longitude: number;
  };
  message?: string;
  timestamp: string;
}

const TrackingScreen: React.FC = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { deliveryId } = route.params as RouteParams;
  const { user } = useAuth();
  const {
    currentLocation,
    trackingHistory,
    estimatedArrival,
    distanceRemaining,
    isTracking,
    startTracking,
    stopTracking,
  } = useTracking(deliveryId);

  const [delivery, setDelivery] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [mapRegion, setMapRegion] = useState({
    latitude: 5.3364,
    longitude: -4.0267,
    latitudeDelta: 0.01,
    longitudeDelta: 0.01,
  });
  const mapRef = useRef<MapView>(null);

  useEffect(() => {
    loadDeliveryDetails();
  }, [deliveryId]);

  useEffect(() => {
    if (currentLocation) {
      setMapRegion({
        latitude: currentLocation.latitude,
        longitude: currentLocation.longitude,
        latitudeDelta: 0.01,
        longitudeDelta: 0.01,
      });
    }
  }, [currentLocation]);

  const loadDeliveryDetails = async () => {
    try {
      setLoading(true);
      // Ici, vous chargeriez les détails de la livraison depuis Supabase
      // Pour l'exemple, on utilise des données mockées
      const mockDelivery = {
        id: deliveryId,
        status: 'en_cours',
        client: {
          name: 'Jean Dupont',
          phone: '+225 01 02 03 04 05',
          address: 'Cocody, Abidjan',
        },
        restaurant: {
          name: 'Restaurant Délice',
          address: 'Plateau, Abidjan',
        },
        livreur: {
          name: 'Pierre Martin',
          phone: '+225 06 07 08 09 10',
        },
        destination: {
          latitude: 5.3364,
          longitude: -4.0267,
          address: 'Cocody, Abidjan',
        },
        origin: {
          latitude: 5.3200,
          longitude: -4.0100,
          address: 'Plateau, Abidjan',
        },
      };
      
      setDelivery(mockDelivery);
    } catch (error) {
      console.error('Erreur lors du chargement des détails:', error);
      Alert.alert('Erreur', 'Impossible de charger les détails de la livraison');
    } finally {
      setLoading(false);
    }
  };

  const handleCallClient = () => {
    if (delivery?.client?.phone) {
      Linking.openURL(`tel:${delivery.client.phone}`);
    }
  };

  const handleCallDeliveryPerson = () => {
    if (delivery?.livreur?.phone) {
      Linking.openURL(`tel:${delivery.livreur.phone}`);
    }
  };

  const handleOpenChat = async () => {
    try {
      const chatId = await chatService.getOrCreateChatForDelivery(deliveryId);
      navigation.navigate('Chat', { deliveryId, chatId });
    } catch (error) {
      console.error('Erreur lors de l\'ouverture du chat:', error);
      Alert.alert('Erreur', 'Impossible d\'ouvrir le chat');
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'en_attente':
        return '#FF9800';
      case 'en_preparation':
        return '#2196F3';
      case 'en_cours':
        return '#4CAF50';
      case 'livree':
        return '#8BC34A';
      case 'annulee':
        return '#F44336';
      default:
        return '#757575';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'en_attente':
        return 'En attente';
      case 'en_preparation':
        return 'En préparation';
      case 'en_cours':
        return 'En cours de livraison';
      case 'livree':
        return 'Livrée';
      case 'annulee':
        return 'Annulée';
      default:
        return status;
    }
  };

  const renderTrackingEvent = (event: TrackingEvent, index: number) => (
    <View key={event.id} style={styles.trackingEvent}>
      <View style={styles.eventIndicator}>
        <View style={[
          styles.eventDot,
          { backgroundColor: index === 0 ? '#4CAF50' : '#E0E0E0' }
        ]} />
        {index < trackingHistory.length - 1 && (
          <View style={styles.eventLine} />
        )}
      </View>
      
      <View style={styles.eventContent}>
        <Text style={styles.eventStatus}>
          {getStatusText(event.status)}
        </Text>
        {event.message && (
          <Text style={styles.eventMessage}>
            {event.message}
          </Text>
        )}
        <Text style={styles.eventTime}>
          {new Date(event.timestamp).toLocaleString('fr-FR')}
        </Text>
      </View>
    </View>
  );

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#007AFF" />
          <Text style={styles.loadingText}>Chargement...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color="#333" />
        </TouchableOpacity>
        
        <Text style={styles.headerTitle}>Suivi de livraison</Text>
        
        <TouchableOpacity
          style={styles.chatButton}
          onPress={handleOpenChat}
        >
          <Ionicons name="chatbubbles-outline" size={24} color="#007AFF" />
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Status Card */}
        <View style={styles.statusCard}>
          <View style={styles.statusHeader}>
            <View style={[
              styles.statusIndicator,
              { backgroundColor: getStatusColor(delivery?.status) }
            ]} />
            <Text style={styles.statusText}>
              {getStatusText(delivery?.status)}
            </Text>
          </View>
          
          {estimatedArrival && (
            <Text style={styles.estimatedTime}>
              Arrivée estimée: {new Date(estimatedArrival).toLocaleTimeString('fr-FR')}
            </Text>
          )}
          
          {distanceRemaining && (
            <Text style={styles.distance}>
              Distance restante: {distanceRemaining.toFixed(1)} km
            </Text>
          )}
        </View>

        {/* Map */}
        <View style={styles.mapContainer}>
          <MapView
            ref={mapRef}
            style={styles.map}
            provider={PROVIDER_GOOGLE}
            region={mapRegion}
            showsUserLocation={true}
            showsMyLocationButton={true}
          >
            {/* Origin Marker */}
            {delivery?.origin && (
              <Marker
                coordinate={delivery.origin}
                title="Restaurant"
                description={delivery.restaurant?.name}
                pinColor="#FF9800"
              />
            )}
            
            {/* Destination Marker */}
            {delivery?.destination && (
              <Marker
                coordinate={delivery.destination}
                title="Destination"
                description={delivery.client?.address}
                pinColor="#4CAF50"
              />
            )}
            
            {/* Current Location Marker */}
            {currentLocation && (
              <Marker
                coordinate={currentLocation}
                title="Livreur"
                description="Position actuelle"
                pinColor="#2196F3"
              >
                <View style={styles.deliveryMarker}>
                  <Ionicons name="bicycle" size={20} color="white" />
                </View>
              </Marker>
            )}
            
            {/* Route Polyline */}
            {delivery?.origin && delivery?.destination && currentLocation && (
              <Polyline
                coordinates={[
                  delivery.origin,
                  currentLocation,
                  delivery.destination,
                ]}
                strokeColor="#007AFF"
                strokeWidth={3}
                lineDashPattern={[5, 5]}
              />
            )}
          </MapView>
        </View>

        {/* Contact Actions */}
        <View style={styles.actionsContainer}>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={handleCallClient}
          >
            <Ionicons name="call" size={20} color="#4CAF50" />
            <Text style={styles.actionText}>Appeler client</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={styles.actionButton}
            onPress={handleCallDeliveryPerson}
          >
            <Ionicons name="call" size={20} color="#2196F3" />
            <Text style={styles.actionText}>Appeler livreur</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={styles.actionButton}
            onPress={handleOpenChat}
          >
            <Ionicons name="chatbubbles" size={20} color="#FF9800" />
            <Text style={styles.actionText}>Chat</Text>
          </TouchableOpacity>
        </View>

        {/* Tracking History */}
        <View style={styles.historyContainer}>
          <Text style={styles.historyTitle}>Historique de suivi</Text>
          
          {trackingHistory.length > 0 ? (
            trackingHistory.map((event, index) => renderTrackingEvent(event, index))
          ) : (
            <View style={styles.emptyHistory}>
              <Ionicons name="time-outline" size={48} color="#E0E0E0" />
              <Text style={styles.emptyHistoryText}>
                Aucun événement de suivi
              </Text>
            </View>
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F5F5',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  chatButton: {
    padding: 8,
  },
  content: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
  },
  statusCard: {
    backgroundColor: 'white',
    margin: 16,
    padding: 16,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  statusHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  statusIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  statusText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  estimatedTime: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  distance: {
    fontSize: 14,
    color: '#666',
  },
  mapContainer: {
    height: 300,
    marginHorizontal: 16,
    marginBottom: 16,
    borderRadius: 12,
    overflow: 'hidden',
  },
  map: {
    flex: 1,
  },
  deliveryMarker: {
    backgroundColor: '#2196F3',
    borderRadius: 20,
    padding: 8,
    borderWidth: 2,
    borderColor: 'white',
  },
  actionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    backgroundColor: 'white',
    marginHorizontal: 16,
    marginBottom: 16,
    padding: 16,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  actionButton: {
    alignItems: 'center',
    padding: 8,
  },
  actionText: {
    fontSize: 12,
    color: '#333',
    marginTop: 4,
    textAlign: 'center',
  },
  historyContainer: {
    backgroundColor: 'white',
    marginHorizontal: 16,
    marginBottom: 16,
    padding: 16,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  historyTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 16,
  },
  trackingEvent: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  eventIndicator: {
    alignItems: 'center',
    marginRight: 12,
  },
  eventDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
  },
  eventLine: {
    width: 2,
    height: 40,
    backgroundColor: '#E0E0E0',
    marginTop: 4,
  },
  eventContent: {
    flex: 1,
  },
  eventStatus: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
    marginBottom: 4,
  },
  eventMessage: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  eventTime: {
    fontSize: 12,
    color: '#999',
  },
  emptyHistory: {
    alignItems: 'center',
    paddingVertical: 32,
  },
  emptyHistoryText: {
    fontSize: 16,
    color: '#999',
    marginTop: 8,
  },
});

export default TrackingScreen;
