import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { useAuth } from '../hooks/useAuth';
import { useNotifications } from '../hooks/useNotifications';
import { useTracking } from '../hooks/useTracking';
import { useChat } from '../hooks/useChat';
import { useAnalytics } from '../hooks/useAnalytics';
import { NotificationService } from '../services/notificationService';
import { 
  notificationService, 
  chatService, 
  trackingService, 
  analyticsService 
} from '../services/supabase';

const TestAdvancedFeaturesScreen: React.FC = () => {
  const navigation = useNavigation();
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [testResults, setTestResults] = useState<Record<string, boolean>>({});

  // Test des hooks
  const { notifications, unreadCount } = useNotifications();
  const { currentLocation, isTracking } = useTracking('test-delivery-id');
  const { messages, participants } = useChat('test-delivery-id');
  const { data: analyticsData, loading: analyticsLoading } = useAnalytics();

  const runTest = async (testName: string, testFunction: () => Promise<void>) => {
    try {
      setLoading(true);
      await testFunction();
      setTestResults(prev => ({ ...prev, [testName]: true }));
      Alert.alert('✅ Test réussi', `${testName} fonctionne correctement`);
    } catch (error) {
      console.error(`Erreur test ${testName}:`, error);
      setTestResults(prev => ({ ...prev, [testName]: false }));
      Alert.alert('❌ Test échoué', `${testName}: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const testNotificationService = async () => {
    if (!user) throw new Error('Utilisateur non connecté');
    
    // Test d'initialisation
    const token = await NotificationService.initialize(user.id);
    if (!token) throw new Error('Impossible d\'obtenir le token push');
    
    // Test de notification locale
    await NotificationService.sendLocalNotification(
      'Test Notification',
      'Ceci est un test des notifications',
      { type: 'test' }
    );
    
    // Test de notification push
    await NotificationService.notifyOrderStatusChange(
      user.id,
      'test-order-id',
      'confirmee',
      'Restaurant Test'
    );
  };

  const testChatService = async () => {
    if (!user) throw new Error('Utilisateur non connecté');
    
    const testDeliveryId = 'test-delivery-id';
    
    // Test de création de chat
    const chatId = await chatService.getOrCreateChatForDelivery(testDeliveryId);
    if (!chatId) throw new Error('Impossible de créer le chat');
    
    // Test d\'envoi de message
    await chatService.sendMessage(
      chatId,
      testDeliveryId,
      user.id,
      'Message de test',
      'text'
    );
    
    // Test de récupération des messages
    const messages = await chatService.getMessages(chatId, testDeliveryId, 10);
    if (!Array.isArray(messages)) throw new Error('Messages non récupérés');
  };

  const testTrackingService = async () => {
    const testDeliveryId = 'test-delivery-id';
    
    // Test d'ajout d'événement de tracking
    await trackingService.addEvent(
      testDeliveryId,
      'test_status',
      { latitude: 5.3364, longitude: -4.0267 },
      'Test de tracking'
    );
    
    // Test de mise à jour de position
    await trackingService.updateDeliveryLocation(
      testDeliveryId,
      5.3364,
      -4.0267,
      new Date(Date.now() + 30 * 60 * 1000), // +30 minutes
      2.5 // 2.5 km restants
    );
    
    // Test de récupération de l'historique
    const history = await trackingService.getHistory(testDeliveryId);
    if (!Array.isArray(history)) throw new Error('Historique non récupéré');
  };

  const testAnalyticsService = async () => {
    if (!user) throw new Error('Utilisateur non connecté');
    
    // Test d'enregistrement d'événement
    await analyticsService.trackEvent(
      user.id,
      'test_event',
      { test_property: 'test_value' },
      'test_session'
    );
    
    // Test de récupération d'événements
    const events = await analyticsService.getUserEvents(
      user.id,
      new Date(Date.now() - 24 * 60 * 60 * 1000), // -24h
      new Date()
    );
    
    if (!Array.isArray(events)) throw new Error('Événements non récupérés');
    
    // Test de métriques de session
    const sessionMetrics = await analyticsService.getSessionMetrics('test_session');
    console.log('Session metrics:', sessionMetrics);
  };

  const testDatabaseConnection = async () => {
    // Test de connexion à la base de données
    const { data, error } = await notificationService.getByUser(user?.id || 'test', false);
    if (error) throw new Error(`Erreur DB: ${error.message}`);
    console.log('Connexion DB réussie, notifications:', data?.length || 0);
  };

  const renderTestButton = (
    title: string,
    testKey: string,
    testFunction: () => Promise<void>,
    icon: string
  ) => {
    const isSuccess = testResults[testKey] === true;
    const isFailure = testResults[testKey] === false;
    
    return (
      <TouchableOpacity
        style={[
          styles.testButton,
          isSuccess && styles.successButton,
          isFailure && styles.failureButton
        ]}
        onPress={() => runTest(testKey, testFunction)}
        disabled={loading}
      >
        <View style={styles.testButtonContent}>
          <Ionicons 
            name={icon as any} 
            size={24} 
            color={isSuccess ? 'white' : isFailure ? 'white' : '#007AFF'} 
          />
          <Text style={[
            styles.testButtonText,
            isSuccess && styles.successText,
            isFailure && styles.failureText
          ]}>
            {title}
          </Text>
          {isSuccess && (
            <Ionicons name="checkmark-circle" size={20} color="white" />
          )}
          {isFailure && (
            <Ionicons name="close-circle" size={20} color="white" />
          )}
        </View>
      </TouchableOpacity>
    );
  };

  const renderStatusCard = (title: string, value: any, icon: string) => (
    <View style={styles.statusCard}>
      <Ionicons name={icon as any} size={24} color="#007AFF" />
      <Text style={styles.statusTitle}>{title}</Text>
      <Text style={styles.statusValue}>{String(value)}</Text>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color="#333" />
        </TouchableOpacity>
        
        <Text style={styles.headerTitle}>Test Fonctionnalités Avancées</Text>
        
        <View style={styles.headerRight} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Status des hooks */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>État des Hooks</Text>
          <View style={styles.statusGrid}>
            {renderStatusCard('Notifications', unreadCount, 'notifications-outline')}
            {renderStatusCard('Messages', messages.length, 'chatbubbles-outline')}
            {renderStatusCard('Participants', participants.length, 'people-outline')}
            {renderStatusCard('Tracking', isTracking ? 'Actif' : 'Inactif', 'location-outline')}
          </View>
        </View>

        {/* Tests des services */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Tests des Services</Text>
          
          {renderTestButton(
            'Test Notifications',
            'notifications',
            testNotificationService,
            'notifications-outline'
          )}
          
          {renderTestButton(
            'Test Chat',
            'chat',
            testChatService,
            'chatbubbles-outline'
          )}
          
          {renderTestButton(
            'Test Tracking',
            'tracking',
            testTrackingService,
            'location-outline'
          )}
          
          {renderTestButton(
            'Test Analytics',
            'analytics',
            testAnalyticsService,
            'analytics-outline'
          )}
          
          {renderTestButton(
            'Test Base de Données',
            'database',
            testDatabaseConnection,
            'server-outline'
          )}
        </View>

        {/* Navigation vers les écrans */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Navigation vers les Écrans</Text>
          
          <TouchableOpacity
            style={styles.navigationButton}
            onPress={() => navigation.navigate('Notifications' as never)}
          >
            <Ionicons name="notifications-outline" size={24} color="#007AFF" />
            <Text style={styles.navigationButtonText}>Écran Notifications</Text>
            <Ionicons name="chevron-forward" size={20} color="#999" />
          </TouchableOpacity>
          
          <TouchableOpacity
            style={styles.navigationButton}
            onPress={() => navigation.navigate('Chat' as never, { deliveryId: 'test-delivery-id' } as never)}
          >
            <Ionicons name="chatbubbles-outline" size={24} color="#007AFF" />
            <Text style={styles.navigationButtonText}>Écran Chat</Text>
            <Ionicons name="chevron-forward" size={20} color="#999" />
          </TouchableOpacity>
          
          <TouchableOpacity
            style={styles.navigationButton}
            onPress={() => navigation.navigate('Tracking' as never, { deliveryId: 'test-delivery-id' } as never)}
          >
            <Ionicons name="location-outline" size={24} color="#007AFF" />
            <Text style={styles.navigationButtonText}>Écran Tracking</Text>
            <Ionicons name="chevron-forward" size={20} color="#999" />
          </TouchableOpacity>
          
          <TouchableOpacity
            style={styles.navigationButton}
            onPress={() => navigation.navigate('Analytics' as never)}
          >
            <Ionicons name="analytics-outline" size={24} color="#007AFF" />
            <Text style={styles.navigationButtonText}>Écran Analytics</Text>
            <Ionicons name="chevron-forward" size={20} color="#999" />
          </TouchableOpacity>
        </View>

        {/* Informations système */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Informations Système</Text>
          <View style={styles.infoContainer}>
            <Text style={styles.infoText}>Utilisateur: {user?.full_name || 'Non connecté'}</Text>
            <Text style={styles.infoText}>Rôle: {user?.role || 'Aucun'}</Text>
            <Text style={styles.infoText}>Position: {currentLocation ? 'Disponible' : 'Non disponible'}</Text>
            <Text style={styles.infoText}>Analytics: {analyticsLoading ? 'Chargement...' : 'Prêt'}</Text>
          </View>
        </View>
      </ScrollView>

      {loading && (
        <View style={styles.loadingOverlay}>
          <ActivityIndicator size="large" color="#007AFF" />
          <Text style={styles.loadingText}>Test en cours...</Text>
        </View>
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F5F5',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  headerRight: {
    width: 40,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 16,
  },
  statusGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  statusCard: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    width: '48%',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  statusTitle: {
    fontSize: 12,
    color: '#666',
    marginTop: 8,
    textAlign: 'center',
  },
  statusValue: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginTop: 4,
  },
  testButton: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  successButton: {
    backgroundColor: '#4CAF50',
  },
  failureButton: {
    backgroundColor: '#F44336',
  },
  testButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  testButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
    flex: 1,
    marginLeft: 12,
  },
  successText: {
    color: 'white',
  },
  failureText: {
    color: 'white',
  },
  navigationButton: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    flexDirection: 'row',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  navigationButtonText: {
    fontSize: 16,
    color: '#333',
    flex: 1,
    marginLeft: 12,
  },
  infoContainer: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  infoText: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
  },
  loadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    color: 'white',
    marginTop: 16,
    fontSize: 16,
  },
});

export default TestAdvancedFeaturesScreen;
