const { getDefaultConfig } = require('expo/metro-config');

const config = getDefaultConfig(__dirname);

// Configuration optimisée pour Expo avec modules natifs
config.resolver.platforms = ['ios', 'android', 'native', 'web'];

// Désactiver sélectivement les polyfills Node.js problématiques
config.resolver.alias = {
  ...config.resolver.alias,
  // Désactiver seulement les modules Node.js qui causent vraiment des problèmes
  'ws': false,
  'stream': false,
  'fs': false,
  'path': false,
  'os': false,
  'http': false,
  'https': false,
  'punycode': false,
  'querystring': false,
  'string_decoder': false,
  'tty': false,
  'vm': false,
  'zlib': false,
  'net': false,
  'tls': false,
  // Préserver events, crypto, util, etc. qui peuvent être nécessaires
};

// Extensions supportées
config.resolver.sourceExts = [...config.resolver.sourceExts, 'cjs'];

// Désactiver les transformations Node.js
config.transformer = {
  ...config.transformer,
  minifierConfig: {
    keep_fnames: true,
    mangle: {
      keep_fnames: true,
    },
  },
};

module.exports = config;
