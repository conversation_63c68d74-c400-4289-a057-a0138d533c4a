// IMPORTANT: Configuration gesture-handler doit être importée EN PREMIER
import './gesture-handler';

import { registerRootComponent } from 'expo';

import App from './App';

// Désactiver les polyfills Node.js problématiques APRÈS l'initialisation
import './global';

// registerRootComponent calls AppRegistry.registerComponent('main', () => App);
// It also ensures that whether you load the app in Expo Go or in a native build,
// the environment is set up appropriately
registerRootComponent(App);
