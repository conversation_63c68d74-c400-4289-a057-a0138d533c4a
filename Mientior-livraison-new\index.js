// IMPORTANT: react-native-gesture-handler doit être importé EN PREMIER
import 'react-native-gesture-handler';

// Désactiver les polyfills Node.js problématiques AVANT toute autre importation
import './global';

import { registerRootComponent } from 'expo';

import App from './App';

// registerRootComponent calls AppRegistry.registerComponent('main', () => App);
// It also ensures that whether you load the app in Expo Go or in a native build,
// the environment is set up appropriately
registerRootComponent(App);
