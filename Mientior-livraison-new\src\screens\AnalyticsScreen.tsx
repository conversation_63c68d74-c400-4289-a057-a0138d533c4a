import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  ActivityIndicator,
  Dimensions,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'react-native-chart-kit';
import { useAuth } from '../hooks/useAuth';
import { useAnalytics } from '../hooks/useAnalytics';

const { width: screenWidth } = Dimensions.get('window');

const AnalyticsScreen: React.FC = () => {
  const navigation = useNavigation();
  const { user } = useAuth();
  const {
    data,
    loading,
    period,
    setPeriod,
    refresh,
  } = useAnalytics();

  const [refreshing, setRefreshing] = useState(false);

  const onRefresh = async () => {
    setRefreshing(true);
    await refresh();
    setRefreshing(false);
  };

  const chartConfig = {
    backgroundColor: '#ffffff',
    backgroundGradientFrom: '#ffffff',
    backgroundGradientTo: '#ffffff',
    decimalPlaces: 0,
    color: (opacity = 1) => `rgba(0, 122, 255, ${opacity})`,
    labelColor: (opacity = 1) => `rgba(0, 0, 0, ${opacity})`,
    style: {
      borderRadius: 16,
    },
    propsForDots: {
      r: '6',
      strokeWidth: '2',
      stroke: '#007AFF',
    },
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'XOF',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const renderMetricCard = (
    title: string,
    value: string | number,
    icon: string,
    color: string,
    subtitle?: string
  ) => (
    <View style={[styles.metricCard, { borderLeftColor: color }]}>
      <View style={styles.metricHeader}>
        <Ionicons name={icon as any} size={24} color={color} />
        <Text style={styles.metricTitle}>{title}</Text>
      </View>
      <Text style={styles.metricValue}>{value}</Text>
      {subtitle && (
        <Text style={styles.metricSubtitle}>{subtitle}</Text>
      )}
    </View>
  );

  const renderPeriodSelector = () => (
    <View style={styles.periodSelector}>
      {['day', 'week', 'month', 'year'].map((p) => (
        <TouchableOpacity
          key={p}
          style={[
            styles.periodButton,
            period === p && styles.activePeriodButton
          ]}
          onPress={() => setPeriod(p as any)}
        >
          <Text style={[
            styles.periodButtonText,
            period === p && styles.activePeriodButtonText
          ]}>
            {p === 'day' ? 'Jour' : 
             p === 'week' ? 'Semaine' :
             p === 'month' ? 'Mois' : 'Année'}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );

  const renderOrdersChart = () => {
    if (!data?.periodData || data.periodData.length === 0) {
      return (
        <View style={styles.emptyChart}>
          <Text style={styles.emptyChartText}>Aucune donnée disponible</Text>
        </View>
      );
    }

    const chartData = {
      labels: data.periodData.slice(-7).map(item => 
        new Date(item.date).toLocaleDateString('fr-FR', { day: '2-digit', month: '2-digit' })
      ),
      datasets: [{
        data: data.periodData.slice(-7).map(item => item.orders),
        color: (opacity = 1) => `rgba(0, 122, 255, ${opacity})`,
        strokeWidth: 2,
      }],
    };

    return (
      <LineChart
        data={chartData}
        width={screenWidth - 32}
        height={220}
        chartConfig={chartConfig}
        bezier
        style={styles.chart}
      />
    );
  };

  const renderRevenueChart = () => {
    if (!data?.periodData || data.periodData.length === 0) {
      return (
        <View style={styles.emptyChart}>
          <Text style={styles.emptyChartText}>Aucune donnée disponible</Text>
        </View>
      );
    }

    const chartData = {
      labels: data.periodData.slice(-7).map(item => 
        new Date(item.date).toLocaleDateString('fr-FR', { day: '2-digit', month: '2-digit' })
      ),
      datasets: [{
        data: data.periodData.slice(-7).map(item => item.revenue),
      }],
    };

    return (
      <BarChart
        data={chartData}
        width={screenWidth - 32}
        height={220}
        chartConfig={{
          ...chartConfig,
          color: (opacity = 1) => `rgba(76, 175, 80, ${opacity})`,
        }}
        style={styles.chart}
      />
    );
  };

  const renderTopProducts = () => {
    if (!data?.topProducts || data.topProducts.length === 0) {
      return (
        <View style={styles.emptySection}>
          <Text style={styles.emptySectionText}>Aucun produit populaire</Text>
        </View>
      );
    }

    return (
      <View style={styles.topItemsContainer}>
        {data.topProducts.slice(0, 5).map((product, index) => (
          <View key={product.id} style={styles.topItem}>
            <View style={styles.topItemRank}>
              <Text style={styles.topItemRankText}>{index + 1}</Text>
            </View>
            <View style={styles.topItemInfo}>
              <Text style={styles.topItemName}>{product.name}</Text>
              <Text style={styles.topItemStats}>
                {product.orders} commandes • {formatCurrency(product.revenue)}
              </Text>
            </View>
            <View style={styles.topItemRating}>
              <Ionicons name="star" size={16} color="#FFD700" />
              <Text style={styles.topItemRatingText}>
                {product.rating.toFixed(1)}
              </Text>
            </View>
          </View>
        ))}
      </View>
    );
  };

  const renderTopRestaurants = () => {
    if (!data?.topRestaurants || data.topRestaurants.length === 0) {
      return (
        <View style={styles.emptySection}>
          <Text style={styles.emptySectionText}>Aucun restaurant populaire</Text>
        </View>
      );
    }

    return (
      <View style={styles.topItemsContainer}>
        {data.topRestaurants.slice(0, 5).map((restaurant, index) => (
          <View key={restaurant.id} style={styles.topItem}>
            <View style={styles.topItemRank}>
              <Text style={styles.topItemRankText}>{index + 1}</Text>
            </View>
            <View style={styles.topItemInfo}>
              <Text style={styles.topItemName}>{restaurant.name}</Text>
              <Text style={styles.topItemStats}>
                {restaurant.orders} commandes • {formatCurrency(restaurant.revenue)}
              </Text>
            </View>
            <View style={styles.topItemRating}>
              <Ionicons name="star" size={16} color="#FFD700" />
              <Text style={styles.topItemRatingText}>
                {restaurant.rating.toFixed(1)}
              </Text>
            </View>
          </View>
        ))}
      </View>
    );
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#007AFF" />
          <Text style={styles.loadingText}>Chargement des analytics...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color="#333" />
        </TouchableOpacity>
        
        <Text style={styles.headerTitle}>Analytics</Text>
        
        <TouchableOpacity
          style={styles.exportButton}
          onPress={() => {
            // Implémenter l'export des données
            console.log('Export des données analytics');
          }}
        >
          <Ionicons name="download-outline" size={24} color="#007AFF" />
        </TouchableOpacity>
      </View>

      {renderPeriodSelector()}

      <ScrollView
        style={styles.content}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        {/* Métriques principales */}
        <View style={styles.metricsGrid}>
          {renderMetricCard(
            'Commandes totales',
            data?.totalOrders || 0,
            'receipt-outline',
            '#4CAF50'
          )}
          {renderMetricCard(
            'Chiffre d\'affaires',
            formatCurrency(data?.totalRevenue || 0),
            'cash-outline',
            '#2196F3'
          )}
          {renderMetricCard(
            'Panier moyen',
            formatCurrency(data?.averageOrderValue || 0),
            'basket-outline',
            '#FF9800'
          )}
          {renderMetricCard(
            'Satisfaction',
            `${(data?.customerSatisfaction || 0).toFixed(1)}/5`,
            'star-outline',
            '#FFD700'
          )}
        </View>

        {/* Graphique des commandes */}
        <View style={styles.chartContainer}>
          <Text style={styles.chartTitle}>Évolution des commandes</Text>
          {renderOrdersChart()}
        </View>

        {/* Graphique du chiffre d'affaires */}
        <View style={styles.chartContainer}>
          <Text style={styles.chartTitle}>Évolution du chiffre d'affaires</Text>
          {renderRevenueChart()}
        </View>

        {/* Top produits */}
        <View style={styles.sectionContainer}>
          <Text style={styles.sectionTitle}>Produits populaires</Text>
          {renderTopProducts()}
        </View>

        {/* Top restaurants */}
        <View style={styles.sectionContainer}>
          <Text style={styles.sectionTitle}>Restaurants populaires</Text>
          {renderTopRestaurants()}
        </View>

        {/* Métriques de performance */}
        <View style={styles.performanceContainer}>
          <Text style={styles.sectionTitle}>Performance</Text>
          <View style={styles.performanceGrid}>
            {renderMetricCard(
              'Temps de livraison moyen',
              `${data?.averageDeliveryTime || 0} min`,
              'time-outline',
              '#9C27B0',
              'Objectif: 30 min'
            )}
            {renderMetricCard(
              'Livraisons à temps',
              `${(data?.onTimeDeliveryRate || 0).toFixed(1)}%`,
              'checkmark-circle-outline',
              '#4CAF50',
              'Objectif: 90%'
            )}
            {renderMetricCard(
              'Taux d\'annulation',
              `${(data?.cancellationRate || 0).toFixed(1)}%`,
              'close-circle-outline',
              '#F44336',
              'Objectif: <5%'
            )}
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F5F5',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  exportButton: {
    padding: 8,
  },
  periodSelector: {
    flexDirection: 'row',
    backgroundColor: 'white',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  periodButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginRight: 8,
    borderRadius: 20,
    backgroundColor: '#F0F0F0',
  },
  activePeriodButton: {
    backgroundColor: '#007AFF',
  },
  periodButtonText: {
    fontSize: 14,
    color: '#666',
  },
  activePeriodButtonText: {
    color: 'white',
    fontWeight: '500',
  },
  content: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
  },
  metricsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    padding: 16,
    gap: 12,
  },
  metricCard: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    width: (screenWidth - 44) / 2,
    borderLeftWidth: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  metricHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  metricTitle: {
    fontSize: 12,
    color: '#666',
    marginLeft: 8,
    flex: 1,
  },
  metricValue: {
    fontSize: 20,
    fontWeight: '700',
    color: '#333',
    marginBottom: 4,
  },
  metricSubtitle: {
    fontSize: 10,
    color: '#999',
  },
  chartContainer: {
    backgroundColor: 'white',
    marginHorizontal: 16,
    marginBottom: 16,
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  chartTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 16,
  },
  chart: {
    borderRadius: 16,
  },
  emptyChart: {
    height: 220,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyChartText: {
    fontSize: 16,
    color: '#999',
  },
  sectionContainer: {
    backgroundColor: 'white',
    marginHorizontal: 16,
    marginBottom: 16,
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 16,
  },
  topItemsContainer: {
    gap: 12,
  },
  topItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
  },
  topItemRank: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: '#007AFF',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  topItemRankText: {
    fontSize: 12,
    fontWeight: '600',
    color: 'white',
  },
  topItemInfo: {
    flex: 1,
  },
  topItemName: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
    marginBottom: 2,
  },
  topItemStats: {
    fontSize: 12,
    color: '#666',
  },
  topItemRating: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  topItemRatingText: {
    fontSize: 12,
    color: '#666',
    marginLeft: 4,
  },
  emptySection: {
    paddingVertical: 32,
    alignItems: 'center',
  },
  emptySectionText: {
    fontSize: 16,
    color: '#999',
  },
  performanceContainer: {
    marginHorizontal: 16,
    marginBottom: 32,
  },
  performanceGrid: {
    gap: 12,
  },
});

export default AnalyticsScreen;
