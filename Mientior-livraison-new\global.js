// Désactiver les polyfills Node.js problématiques de manière sélective
if (typeof global !== 'undefined') {
  // Préserver les éléments nécessaires pour React Native
  global.process = global.process || {};
  global.process.env = global.process.env || {};

  // Mock seulement les modules Node.js spécifiques qui causent des problèmes
  // Ne pas toucher aux globals nécessaires pour react-native-gesture-handler
  global.stream = undefined;
  global.net = undefined;
  global.tls = undefined;
  global.fs = undefined;
  global.path = undefined;
  global.os = undefined;
  global.http = undefined;
  global.https = undefined;
  global.punycode = undefined;
  global.querystring = undefined;
  global.string_decoder = undefined;
  global.tty = undefined;
  global.vm = undefined;
  global.zlib = undefined;

  // Préserver Buffer et setImmediate qui peuvent être nécessaires
  // global.Buffer = undefined;
  // global.setImmediate = undefined;
  // global.clearImmediate = undefined;
}

// Empêcher l'auto-importation de polyfills
if (typeof window !== 'undefined') {
  window.process = window.process || {};
  window.process.env = window.process.env || {};
}

console.log('Polyfills Node.js désactivés pour React Native'); 